"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.User = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const base_entity_1 = require("../../../common/entities/base.entity");
const organization_entity_1 = require("../../organizations/entities/organization.entity");
const position_entity_1 = require("../../positions/entities/position.entity");
const role_entity_1 = require("../../roles/entities/role.entity");
let User = class User extends base_entity_1.BaseEntity {
};
exports.User = User;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '工号' }),
    (0, typeorm_1.Column)({
        name: 'employee_id',
        type: 'varchar',
        length: 50,
        unique: true,
        nullable: true,
        comment: '工号',
    }),
    __metadata("design:type", String)
], User.prototype, "employeeId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户名' }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 50,
        unique: true,
        comment: '用户名',
    }),
    __metadata("design:type", String)
], User.prototype, "username", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 255,
        comment: '密码',
    }),
    __metadata("design:type", String)
], User.prototype, "password", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '真实姓名' }),
    (0, typeorm_1.Column)({
        name: 'real_name',
        type: 'varchar',
        length: 100,
        comment: '真实姓名',
    }),
    __metadata("design:type", String)
], User.prototype, "realName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '手机号' }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 20,
        nullable: true,
        comment: '手机号',
    }),
    __metadata("design:type", String)
], User.prototype, "phone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '邮箱' }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 100,
        nullable: true,
        comment: '邮箱',
    }),
    __metadata("design:type", String)
], User.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '部门ID' }),
    (0, typeorm_1.Column)({
        name: 'department_id',
        type: 'bigint',
        nullable: true,
        comment: '部门ID',
    }),
    __metadata("design:type", Number)
], User.prototype, "departmentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '岗位ID' }),
    (0, typeorm_1.Column)({
        name: 'position_id',
        type: 'bigint',
        nullable: true,
        comment: '岗位ID',
    }),
    __metadata("design:type", Number)
], User.prototype, "positionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态：1启用，0禁用' }),
    (0, typeorm_1.Column)({
        type: 'tinyint',
        default: 1,
        comment: '状态：1启用，0禁用',
    }),
    __metadata("design:type", Number)
], User.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '头像' }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 255,
        nullable: true,
        comment: '头像',
    }),
    __metadata("design:type", String)
], User.prototype, "avatar", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最后登录时间' }),
    (0, typeorm_1.Column)({
        name: 'last_login_time',
        type: 'datetime',
        nullable: true,
        comment: '最后登录时间',
    }),
    __metadata("design:type", Date)
], User.prototype, "lastLoginTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '性别' }),
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 10,
        nullable: true,
        comment: '性别',
    }),
    __metadata("design:type", String)
], User.prototype, "gender", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户组ID列表' }),
    (0, typeorm_1.Column)({
        name: 'user_group_ids',
        type: 'json',
        nullable: true,
        comment: '用户组ID列表',
    }),
    __metadata("design:type", Array)
], User.prototype, "userGroupIds", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '管理级别' }),
    (0, typeorm_1.Column)({
        name: 'management_level',
        type: 'int',
        nullable: true,
        comment: '管理级别',
    }),
    __metadata("design:type", Number)
], User.prototype, "managementLevel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '技术级别' }),
    (0, typeorm_1.Column)({
        name: 'technical_level',
        type: 'int',
        nullable: true,
        comment: '技术级别',
    }),
    __metadata("design:type", Number)
], User.prototype, "technicalLevel", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => organization_entity_1.Organization, { eager: true }),
    (0, typeorm_1.JoinColumn)({ name: 'department_id' }),
    __metadata("design:type", organization_entity_1.Organization)
], User.prototype, "department", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => position_entity_1.Position, { eager: true }),
    (0, typeorm_1.JoinColumn)({ name: 'position_id' }),
    __metadata("design:type", position_entity_1.Position)
], User.prototype, "position", void 0);
__decorate([
    (0, typeorm_1.ManyToMany)(() => role_entity_1.Role, (role) => role.users),
    (0, typeorm_1.JoinTable)({
        name: 'user_roles',
        joinColumn: { name: 'user_id', referencedColumnName: 'id' },
        inverseJoinColumn: { name: 'role_id', referencedColumnName: 'id' },
    }),
    __metadata("design:type", Array)
], User.prototype, "roles", void 0);
exports.User = User = __decorate([
    (0, typeorm_1.Entity)('users'),
    (0, typeorm_1.Index)(['username']),
    (0, typeorm_1.Index)(['departmentId']),
    (0, typeorm_1.Index)(['positionId']),
    (0, typeorm_1.Index)(['status'])
], User);
//# sourceMappingURL=user.entity.js.map