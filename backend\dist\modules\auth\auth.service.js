"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const users_service_1 = require("../users/users.service");
let AuthService = class AuthService {
    constructor(usersService, jwtService) {
        this.usersService = usersService;
        this.jwtService = jwtService;
    }
    async validateUser(account, password) {
        let user = await this.usersService.findByUsername(account);
        if (!user) {
            user = await this.usersService.findByPhone(account);
        }
        if (!user) {
            return null;
        }
        if (user.status !== 1) {
            throw new common_1.UnauthorizedException('用户已被禁用');
        }
        const isPasswordValid = await this.usersService.validatePassword(user, password);
        if (!isPasswordValid) {
            return null;
        }
        return user;
    }
    async login(loginDto) {
        const user = await this.validateUser(loginDto.account, loginDto.password);
        if (!user) {
            throw new common_1.UnauthorizedException('账号或密码错误');
        }
        await this.usersService.updateLastLoginTime(user.id);
        const payload = {
            accountId: user.id.toString(),
            roleId: user.roles?.[0]?.id?.toString() || '',
            appId: '1584770581682618369',
            name: user.realName,
            roleType: '1',
            userId: user.id.toString(),
            account: loginDto.account
        };
        const token = this.jwtService.sign(payload);
        return {
            roleType: 1,
            name: user.realName,
            token,
            account: loginDto.account,
        };
    }
    async logout(_user) {
        return { message: '退出成功' };
    }
    async getProfile(userId) {
        const user = await this.usersService.findById(userId);
        if (!user) {
            throw new common_1.UnauthorizedException('用户不存在');
        }
        return {
            id: user.id,
            username: user.username,
            realName: user.realName,
            email: user.email,
            phone: user.phone,
            avatar: user.avatar,
            department: user.department,
            position: user.position,
            roles: user.roles,
        };
    }
    async changePassword(userId, changePasswordDto) {
        const user = await this.usersService.findById(userId);
        if (!user) {
            throw new common_1.UnauthorizedException('用户不存在');
        }
        const isOldPasswordValid = await this.usersService.validatePassword(user, changePasswordDto.oldPassword);
        if (!isOldPasswordValid) {
            throw new common_1.BadRequestException('原密码错误');
        }
        if (changePasswordDto.oldPassword === changePasswordDto.newPassword) {
            throw new common_1.BadRequestException('新密码不能与原密码相同');
        }
        await this.usersService.updatePassword(userId, changePasswordDto.newPassword);
        return { message: '密码修改成功' };
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [users_service_1.UsersService,
        jwt_1.JwtService])
], AuthService);
//# sourceMappingURL=auth.service.js.map