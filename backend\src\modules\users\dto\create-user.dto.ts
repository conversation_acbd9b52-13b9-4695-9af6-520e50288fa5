import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsEmail, IsOptional, IsNumber, MinLength, IsArray } from 'class-validator';

export class CreateUserDto {
  @ApiProperty({ description: '工号', required: false })
  @IsOptional()
  @IsString()
  employeeId?: string;

  @ApiProperty({ description: '用户名' })
  @IsNotEmpty({ message: '用户名不能为空' })
  @IsString()
  username: string;

  @ApiProperty({ description: '密码' })
  @IsOptional()
  @IsString()
  @MinLength(6, { message: '密码长度不能少于6位' })
  password?: string;

  @ApiProperty({ description: '真实姓名' })
  @IsNotEmpty({ message: '姓名不能为空' })
  @IsString()
  realName: string;

  @ApiProperty({ description: '手机号', required: false })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiProperty({ description: '邮箱', required: false })
  @IsOptional()
  @IsEmail({}, { message: '邮箱格式不正确' })
  email?: string;

  @ApiProperty({ description: '部门ID', required: false })
  @IsOptional()
  @IsNumber()
  departmentId?: number;

  @ApiProperty({ description: '岗位ID', required: false })
  @IsOptional()
  @IsNumber()
  positionId?: number;

  @ApiProperty({ description: '状态：1启用，0禁用', default: 1, required: false })
  @IsOptional()
  @IsNumber()
  status?: number;

  @ApiProperty({ description: '性别', required: false })
  @IsOptional()
  @IsString()
  gender?: string;

  @ApiProperty({ description: '用户组ID列表', required: false })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  userGroupIds?: number[];

  @ApiProperty({ description: '管理级别', required: false })
  @IsOptional()
  @IsNumber()
  managementLevel?: number;

  @ApiProperty({ description: '技术级别', required: false })
  @IsOptional()
  @IsNumber()
  technicalLevel?: number;

  @ApiProperty({ description: '角色ID列表', required: false })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  roleIds?: number[];
}
