import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like } from 'typeorm';
import * as bcrypt from 'bcryptjs';
import { User } from './entities/user.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { QueryUserDto } from './dto/query-user.dto';
import { PaginationResult } from '@/common/dto/pagination.dto';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async create(createUserDto: CreateUserDto, currentUserId?: number): Promise<User> {
    // 检查用户名是否已存在
    const existingUser = await this.userRepository.findOne({
      where: { username: createUserDto.username },
    });
    if (existingUser) {
      throw new ConflictException('用户名已存在');
    }

    // 检查工号是否已存在
    if (createUserDto.employeeId) {
      const existingEmployee = await this.userRepository.findOne({
        where: { employeeId: createUserDto.employeeId },
      });
      if (existingEmployee) {
        throw new ConflictException('工号已存在');
      }
    }

    // 如果提供了密码，则加密密码；否则，使用默认密码
    let hashedPassword = ' ';
    if (createUserDto.password) {
      hashedPassword = await bcrypt.hash(createUserDto.password, 10);
    } else {
      // 如果没有提供密码，则生成一个默认密码
      hashedPassword = await bcrypt.hash('123456', 10); // 默认密码为 '123456'
    }

    const user = this.userRepository.create({
      ...createUserDto,
      password: hashedPassword,
      createdBy: currentUserId,
    });

    return this.userRepository.save(user);
  }

  async findAll(queryDto: QueryUserDto): Promise<PaginationResult<User>> {
    const { page = 1, pageSize = 10, username, realName, status, departmentId, positionId } = queryDto;

    const queryBuilder = this.userRepository.createQueryBuilder('user')
      .leftJoinAndSelect('user.department', 'department')
      .leftJoinAndSelect('user.position', 'position')
      .leftJoinAndSelect('user.roles', 'roles');

    if (username) {
      queryBuilder.andWhere('user.username LIKE :username', { username: `%${username}%` });
    }

    if (realName) {
      queryBuilder.andWhere('user.realName LIKE :realName', { realName: `%${realName}%` });
    }

    if (status !== undefined) {
      queryBuilder.andWhere('user.status = :status', { status });
    }

    if (departmentId) {
      queryBuilder.andWhere('user.departmentId = :departmentId', { departmentId });
    }

    if (positionId) {
      queryBuilder.andWhere('user.positionId = :positionId', { positionId });
    }

    const [list, total] = await queryBuilder
      .skip((page - 1) * pageSize)
      .take(pageSize)
      .getManyAndCount();

    return new PaginationResult(list, total, page, pageSize);
  }

  async findOne(id: number): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['department', 'position', 'roles'],
    });

    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    return user;
  }

  async findById(id: number): Promise<User | null> {
    return this.userRepository.findOne({
      where: { id },
      relations: ['department', 'position', 'roles'],
    });
  }

  async findByUsername(username: string): Promise<User | null> {
    return this.userRepository.findOne({
      where: { username },
      relations: ['department', 'position', 'roles'],
    });
  }

  async findByPhone(phone: string): Promise<User | null> {
    return this.userRepository.findOne({
      where: { phone },
      relations: ['department', 'position', 'roles'],
    });
  }

  async update(id: number, updateUserDto: UpdateUserDto, currentUserId?: number): Promise<User> {
    const user = await this.findOne(id);

    // 检查工号是否已存在（排除当前用户）
    if (updateUserDto.employeeId && updateUserDto.employeeId !== user.employeeId) {
      const existingEmployee = await this.userRepository.findOne({
        where: { employeeId: updateUserDto.employeeId },
      });
      if (existingEmployee && existingEmployee.id !== id) {
        throw new ConflictException('工号已存在');
      }
    }

    // 过滤掉密码字段，因为密码更新应该通过 resetPassword 方法进行
    const { ...restUpdateUserDto } = updateUserDto;
    Object.assign(user, {
      ...restUpdateUserDto,
      updatedBy: currentUserId,
    });

    return this.userRepository.save(user);
  }

  async remove(id: number): Promise<void> {
    const user = await this.findOne(id);
    await this.userRepository.remove(user);
  }

  async batchRemove(ids: number[]): Promise<void> {
    await this.userRepository.delete(ids);
  }

  async resetPassword(id: number, newPassword: string, currentUserId?: number): Promise<void> {
    const user = await this.findOne(id);
    const hashedPassword = await bcrypt.hash(newPassword, 10);
    
    user.password = hashedPassword;
    user.updatedBy = currentUserId;
    
    await this.userRepository.save(user);
  }

  async updatePassword(id: number, newPassword: string, currentUserId?: number): Promise<void> {
    const user = await this.findOne(id);
    const hashedPassword = await bcrypt.hash(newPassword, 10);
    
    user.password = hashedPassword;
    user.updatedBy = currentUserId;
    
    await this.userRepository.save(user);
  }

  async updateStatus(id: number, status: number, currentUserId?: number): Promise<void> {
    const user = await this.findOne(id);
    
    user.status = status;
    user.updatedBy = currentUserId;
    
    await this.userRepository.save(user);
  }

  async updateLastLoginTime(id: number): Promise<void> {
    await this.userRepository.update(id, {
      lastLoginTime: new Date(),
    });
  }

  async validatePassword(user: User, password: string): Promise<boolean> {
    return bcrypt.compare(password, user.password);
  }

  async batchUpdateStatus(ids: number[], status: number, currentUserId?: number): Promise<void> {
    await this.userRepository.update(ids, {
      status,
      updatedBy: currentUserId,
    });
  }

  async batchResetPassword(ids: number[], newPassword: string, currentUserId?: number): Promise<void> {
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    await this.userRepository.update(ids, {
      password: hashedPassword,
      updatedBy: currentUserId,
    });
  }

  async assignRoles(userId: number, roleIds: number[], currentUserId?: number): Promise<void> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['roles'],
    });

    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    // 这里需要根据实际的角色实体来实现
    // 暂时留空，需要角色管理模块完成后再实现
    user.updatedBy = currentUserId;
    await this.userRepository.save(user);
  }

  async batchAssignRoles(userIds: number[], roleIds: number[], currentUserId?: number): Promise<void> {
    // 这里需要根据实际的角色实体来实现
    // 暂时留空，需要角色管理模块完成后再实现
    await this.userRepository.update(userIds, {
      updatedBy: currentUserId,
    });
  }
}
