import { <PERSON><PERSON><PERSON>, Column, Index, ManyToOne, <PERSON>in<PERSON><PERSON><PERSON>n, ManyToMany, JoinT<PERSON> } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Exclude } from 'class-transformer';
import { BaseEntity } from '@/common/entities/base.entity';
import { Organization } from '@/modules/organizations/entities/organization.entity';
import { Position } from '@/modules/positions/entities/position.entity';
import { Role } from '@/modules/roles/entities/role.entity';

@Entity('users')
@Index(['username'])
@Index(['departmentId'])
@Index(['positionId'])
@Index(['status'])
export class User extends BaseEntity {
  @ApiProperty({ description: '工号' })
  @Column({
    name: 'employee_id',
    type: 'varchar',
    length: 50,
    unique: true,
    nullable: true,
    comment: '工号',
  })
  employeeId?: string;

  @ApiProperty({ description: '用户名' })
  @Column({
    type: 'varchar',
    length: 50,
    unique: true,
    comment: '用户名',
  })
  username: string;

  @Exclude()
  @Column({
    type: 'varchar',
    length: 255,
    comment: '密码',
  })
  password: string;

  @ApiProperty({ description: '真实姓名' })
  @Column({
    name: 'real_name',
    type: 'varchar',
    length: 100,
    comment: '真实姓名',
  })
  realName: string;

  @ApiProperty({ description: '手机号' })
  @Column({
    type: 'varchar',
    length: 20,
    nullable: true,
    comment: '手机号',
  })
  phone?: string;

  @ApiProperty({ description: '邮箱' })
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: '邮箱',
  })
  email?: string;

  @ApiProperty({ description: '部门ID' })
  @Column({
    name: 'department_id',
    type: 'bigint',
    nullable: true,
    comment: '部门ID',
  })
  departmentId?: number;

  @ApiProperty({ description: '岗位ID' })
  @Column({
    name: 'position_id',
    type: 'bigint',
    nullable: true,
    comment: '岗位ID',
  })
  positionId?: number;

  @ApiProperty({ description: '状态：1启用，0禁用' })
  @Column({
    type: 'tinyint',
    default: 1,
    comment: '状态：1启用，0禁用',
  })
  status: number;

  @ApiProperty({ description: '头像' })
  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '头像',
  })
  avatar?: string;

  @ApiProperty({ description: '最后登录时间' })
  @Column({
    name: 'last_login_time',
    type: 'datetime',
    nullable: true,
    comment: '最后登录时间',
  })
  lastLoginTime?: Date;

  @ApiProperty({ description: '性别' })
  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
    comment: '性别',
  })
  gender?: string;

  @ApiProperty({ description: '用户组ID列表' })
  @Column({
    name: 'user_group_ids',
    type: 'json',
    nullable: true,
    comment: '用户组ID列表',
  })
  userGroupIds?: number[];

  @ApiProperty({ description: '管理级别' })
  @Column({
    name: 'management_level',
    type: 'int',
    nullable: true,
    comment: '管理级别',
  })
  managementLevel?: number;

  @ApiProperty({ description: '技术级别' })
  @Column({
    name: 'technical_level',
    type: 'int',
    nullable: true,
    comment: '技术级别',
  })
  technicalLevel?: number;

  // 关联关系
  @ManyToOne(() => Organization, { eager: true })
  @JoinColumn({ name: 'department_id' })
  department?: Organization;

  @ManyToOne(() => Position, { eager: true })
  @JoinColumn({ name: 'position_id' })
  position?: Position;

  @ManyToMany(() => Role, (role) => role.users)
  @JoinTable({
    name: 'user_roles',
    joinColumn: { name: 'user_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'role_id', referencedColumnName: 'id' },
  })
  roles?: Role[];
}
