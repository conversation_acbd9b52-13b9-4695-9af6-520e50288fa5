<script lang="ts" setup>
import { computed, ref, unref, watch } from 'vue';
import { useVbenModal, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, TabsContent } from '@vben/common-ui';
import { useVbenForm } from '#/adapter/form';
import { formSchema, buildTreeSelectData, buildSelectOptions, roleOptions, technicalLevelOptions, managementLevelOptions, userGroupOptions } from '../user.data';
import {
  createUser,
  updateUser,
} from '../user.api';
import { getOrganizationTree } from '../../organization/organization.api';
import { getPositionList } from '../../position/position.api';
import { message } from 'ant-design-vue';
import { showLoading, showSuccess } from '#/utils/toast.js';

// 声明Emits
const emit = defineEmits(['register', 'success']);



const isUpdate = ref(false);
const userId = ref<number | null>(null);
const organizationTreeData = ref([]);
const positionOptions = ref<Array<{ value: number; label: string }>>([]);
const userPositions = ref<Array<{ id: number; name: string; code: string; department: { name: string } }>>([]);
const userDepartments = ref<Array<{ departmentId?: number; positionId?: number }>>([]);
const selectedPositionId = ref<number | undefined>();
const activeTab = ref('basic'); // 当前激活的标签页

const [Modal, modalApi] = useVbenModal({
  destroyOnClose: true,
  closeOnClickModal: false,
  onCancel() {
    modalApi.close();
  },
  onClosed() {
    formApi.resetForm();
    isUpdate.value = false;
    userId.value = null;
    userPositions.value = [];
    userDepartments.value = [];
    selectedPositionId.value = undefined;
  },
  onConfirm() {},
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      // 加载表单选项数据
      loadFormOptions();

      const state = modalApi.useStore().value;
      if (state?.row) {
        // 编辑模式
        isUpdate.value = true;
        userId.value = state.row.id;
        formApi.setValues(state.row);
        // 加载用户岗位信息
        loadUserPositions(state.row.id);
      } else {
        // 新增模式
        isUpdate.value = false;
        userId.value = null;
        userPositions.value = [];
        // 初始化部门信息，添加一个默认行
        userDepartments.value = [{
          departmentId: undefined,
          positionId: undefined,
        }];
      }
    }
  },
});



// 加载表单选项数据
const loadFormOptions = async () => {
  try {
    // 加载组织树和岗位列表
    await loadOrganizationTree();
    await loadPositionList();

    // 更新表单schema中的选项数据
    formApi.updateSchema([
      {
        fieldName: 'roleIds',
        componentProps: {
          options: roleOptions,
        },
      },
      {
        fieldName: 'technicalLevel',
        componentProps: {
          options: technicalLevelOptions,
        },
      },
      {
        fieldName: 'managementLevel',
        componentProps: {
          options: managementLevelOptions,
        },
      },
      {
        fieldName: 'userGroupIds',
        componentProps: {
          options: userGroupOptions,
        },
      },
    ]);
  } catch (error) {
    console.error('加载表单选项失败:', error);
  }
};

// 加载组织树数据
const loadOrganizationTree = async () => {
  try {
    const data = await getOrganizationTree();
    organizationTreeData.value = buildTreeSelectData(data);
  } catch (error) {
    console.error('加载组织树失败:', error);
  }
};

// 加载岗位列表
const loadPositionList = async () => {
  try {
    const data = await getPositionList();
    positionOptions.value = buildSelectOptions(data);
  } catch (error) {
    console.error('加载岗位列表失败:', error);
  }
};

// 添加部门
const handleAddDepartment = () => {
  userDepartments.value.push({
    departmentId: undefined,
    positionId: undefined,
  });
};

// 删除部门
const handleRemoveDepartment = (index: number) => {
  userDepartments.value.splice(index, 1);
};

// 加载用户岗位信息
const loadUserPositions = async (userId: number) => {
  try {
    // 这里应该调用获取用户岗位的API，暂时使用空数组
    userPositions.value = [];
  } catch (error) {
    console.error('加载用户岗位失败:', error);
  }
};



const [Form, formApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
    labelClass: 'text-right pr-4',
    labelWidth: 100,
  },
  // 提交函数
  handleSubmit,
  handleReset,
  // 垂直布局，label和input在不同行，值为vertical
  // 水平布局，label和input在同一行
  layout: 'horizontal',
  schema: formSchema,
  wrapperClass: 'grid-cols-2',
  showDefaultActions:false,
});

// 计算弹窗标题
const title = computed(() => {
  return isUpdate.value ? '编辑用户' : '新增用户';
});

// 表单重置事件
async function handleReset() {
  modalApi.close();
}

// 表单提交事件
async function handleSubmit() {
  let  values = await formApi.getValues();
  try {
    showLoading('操作处理中...');

    // 添加用户部门信息
    const submitData = {
      ...values,
      departments: userDepartments.value,
    };

    if (isUpdate.value && userId.value) {
      await updateUser(userId.value, submitData);
      showSuccess('更新成功！');
    } else {
      await createUser(submitData);
      showSuccess('创建成功！');
    }

    modalApi.close();
    emit('success');
  } catch (error) {
    console.error('操作失败:', error);
    // message.error(error.message || '操作失败');
  }
}

// 打开新增弹窗
function openCreateModal(departmentId?: number) {
  isUpdate.value = false;
  userId.value = null;
  modalApi.open();

  if (departmentId) {
    formApi.setFieldValue('departmentId', departmentId);
  }
}

// 打开编辑弹窗
function openEditModal(record: any) {
  isUpdate.value = true;
  userId.value = record.id;
  modalApi.open();

  // 设置表单值
  formApi.setValues(record);
}

// 暴露方法给父组件
defineExpose({
  openCreateModal,
  openEditModal,
});


</script>

<template>
  <Modal
    v-bind="$attrs"
    :footer="false"
    :title="title"
    class="w-[850px]"
    :destroy-on-close="true"
    :maskClosable="false"
  >
    <div style="padding: 0px">
      <Tabs v-model="activeTab" class="w-full">
        <TabsList class="w-full">
          <TabsTrigger value="basic">基础信息</TabsTrigger>
          <TabsTrigger value="departments">部门信息</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" class="tab-content">
          <div class="px-5 py-4">
            <div class="hide-form-buttons">
              <Form />
            </div>
            <!-- 单独一行的按钮 -->
            <div class="flex justify-end gap-2 mt-6 pt-4 ">
              <a-button @click="handleReset">
                取消
              </a-button>
              <a-button type="primary" @click="() => handleSubmit()">
                确定
              </a-button>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="departments" class="tab-content">
          <div class="px-5 py-4">
            <!-- 添加部门 -->
            <div class="mb-4 flex items-center gap-2">
              <a-button type="primary" @click="handleAddDepartment">
                新增
              </a-button>
            </div>

            <!-- 部门信息表格 -->
            <div class="border rounded">
              <table class="w-full">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-4 py-2 text-left border-b">任职部门</th>
                    <th class="px-4 py-2 text-left border-b">岗位</th>
                    <th class="px-4 py-2 text-left border-b">操作</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(dept, index) in userDepartments" :key="index" class="border-b">
                    <td class="px-4 py-2">
                      <span v-if="index === 0">默认显示当前机构树选中部门</span>
                      <a-select
                        v-else
                        v-model="dept.departmentId"
                        placeholder="请选择部门"
                        :options="organizationTreeData"
                        show-search
                        option-filter-prop="title"
                        class="w-full"
                      />
                    </td>
                    <td class="px-4 py-2">
                      <a-select
                        v-model="dept.positionId"
                        placeholder="请选择岗位"
                        :options="positionOptions"
                        show-search
                        option-filter-prop="label"
                        class="w-full"
                      />
                    </td>
                    <td class="px-4 py-2">
                      <a-button
                        type="link"
                        danger
                        @click="handleRemoveDepartment(index)"
                      >
                        删除
                      </a-button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  </Modal>
</template>

<style scoped lang="less">
@import '#/styles/dark-antd.less';

.tab-content {
  min-height: 500px;
}

// 隐藏表单默认按钮
.hide-form-buttons {
  :deep(.ant-form-item:last-child) {
    display: none;
  }

  :deep(.form-footer) {
    display: none;
  }

  :deep(.ant-form-item-control-input) {
    .ant-form-item-control-input-content {
      .ant-btn {
        display: none;
      }
    }
  }
}
</style>
