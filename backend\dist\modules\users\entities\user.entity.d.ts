import { BaseEntity } from '@/common/entities/base.entity';
import { Organization } from '@/modules/organizations/entities/organization.entity';
import { Position } from '@/modules/positions/entities/position.entity';
import { Role } from '@/modules/roles/entities/role.entity';
export declare class User extends BaseEntity {
    employeeId?: string;
    username: string;
    password: string;
    realName: string;
    phone?: string;
    email?: string;
    departmentId?: number;
    positionId?: number;
    status: number;
    avatar?: string;
    lastLoginTime?: Date;
    gender?: string;
    userGroupIds?: number[];
    managementLevel?: number;
    technicalLevel?: number;
    department?: Organization;
    position?: Position;
    roles?: Role[];
}
